const sidebarOpen = "translateX(0)";
const sidebarClosed = "translateX(-260px)";
const sidebarWidth = "260px";
const sidebarClose = "200px";

const sidebar = document.getElementById("sidebar");
const themeSwitch = document.getElementById("theme-switch");
const backgroundBlur = document.getElementById("background-blur");
const body = document.body;
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

let isOpen = false;

// Toggle Sidebar functions
const toggleSidebar = () => {
    const transform = isOpen ? sidebarClosed : sidebarOpen;
    const width = isOpen ? sidebarClose : sidebarWidth;

    sidebar.style.transform = transform;
    sidebar.style.width = width;
    isOpen = !isOpen;

    
    if (window.innerWidth <= 1100) {
        backgroundBlur.style.visibility = isOpen ? 'visible' : 'hidden';  
        setTimeout(() => {
            backgroundBlur.style.opacity = isOpen ? '1' : '0';
        }, 50);
    }

};

const showSidebar = () => {
    sidebar.style.transform = sidebarOpen;
    sidebar.style.width = sidebarWidth;
    isOpen = true;
};

const hideSidebar = () => {
    sidebar.style.transform = sidebarClosed;
    sidebar.style.width = sidebarClose;
    isOpen = false;

    backgroundBlur.style.opacity = "0";
    backgroundBlur.style.visibility = "hidden";
};

// Toggle Theme functions
const applyTheme = (theme) => {
    const isDark = theme === 'dark';

    body.className = isDark ? 'dark-mode' : 'light-mode';
    themeSwitch.checked = !isDark;
};

const initTheme = () => {
    const savedTheme = localStorage.getItem('theme');

    if (savedTheme) {
        applyTheme(savedTheme);
    } else {
        const systemTheme = mediaQuery.matches ? 'dark' : 'light';
        applyTheme(systemTheme);
    }
};

const handleThemeChange = () => {
    const newTheme = themeSwitch.checked ? 'light' : 'dark';
    applyTheme(newTheme);
    localStorage.setItem('theme', newTheme);
};

const handleSystemThemeChange = (e) => {
    localStorage.removeItem('theme');
    applyTheme(e.matches ? 'dark' : 'light');
};

// Bind events
themeSwitch.addEventListener('change', handleThemeChange);
mediaQuery.addEventListener('change', handleSystemThemeChange);

// Initialize
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTheme);
} else {
    initTheme();
}

// Long press context menu for files
const fileListContainer = document.getElementById('fileList');
const contextMenu = document.getElementById('file-context-menu');
const preview = document.getElementById('content-preview');
const overlay = document.getElementById('context-menu-overlay');

let contextMenuTimer;
let contextMenuVisible = false;
let currentFileId = null;
let currentFileElement = null; 
let startX = 0;
let startY = 0;
let isDragging = false; // For mobile drag-to-select

const showContextMenu = (x, y, fileItem) => {
    currentFileId = fileItem.dataset.fileId;
    currentFileElement = fileItem; // Store the element

    if (fileItem) {
        const rect = fileItem.getBoundingClientRect(); // Get rect BEFORE hiding
        currentFileElement.style.visibility = 'hidden'; 

        preview.style.top = `${rect.top}px`;
        preview.style.left = `${rect.left}px`;
        preview.style.width = `${rect.width}px`;
        preview.style.height = `${rect.height}px`;
        
        const clonedNode = fileItem.cloneNode(true);
        clonedNode.style.visibility = 'visible'; 
        clonedNode.style.margin = '0'; 
        const actions = clonedNode.querySelector('.file-actions');
        if (actions) {
            actions.style.opacity = '1';
        }
        preview.innerHTML = ''; 
        preview.appendChild(clonedNode);

        const menuWidth = contextMenu.offsetWidth || 180;
        const menuHeight = contextMenu.offsetHeight || 80;
        
        let menuLeft = rect.left;
        let menuTop = rect.bottom + 20; 

        if (menuLeft + menuWidth > window.innerWidth) {
            menuLeft = window.innerWidth - menuWidth - 5;
        }
        if (menuTop + menuHeight > window.innerHeight) {
            menuTop = rect.top - menuHeight - 5; // Place it above
        }
        
        contextMenu.style.left = `${menuLeft}px`;
        contextMenu.style.top = `${menuTop}px`;
    }

    // Make elements visible
    preview.style.visibility = 'visible';
    preview.style.opacity = '1';
    preview.style.transform = 'scale(1)';

    contextMenu.style.opacity = '1';
    contextMenu.style.transform = 'scale(1)';
    contextMenu.style.pointerEvents = 'all';

    overlay.style.visibility = 'visible';
    overlay.style.opacity = '1';
    
    contextMenuVisible = true;
    isDragging = true;
};

const hideContextMenu = () => {
    if (currentFileElement) {
        currentFileElement.style.visibility = 'visible';
        currentFileElement = null;
    }
    
    // Hide elements with transition
    preview.style.opacity = '0';
    preview.style.transform = 'scale(0.98)';
    
    contextMenu.style.opacity = '0';
    contextMenu.style.transform = 'scale(0.8)';
    contextMenu.style.pointerEvents = 'none';

    overlay.style.opacity = '0';

    setTimeout(() => {
        if (isDragging) return;
        
        preview.innerHTML = '';
        preview.style.visibility = 'hidden';
        overlay.style.visibility = 'hidden';
    }, 200);

    contextMenuVisible = false;
    currentFileId = null;
    isDragging = false;
};

fileListContainer.addEventListener('mousedown', e => {
    const fileItem = e.target.closest('.file-item');
    if (!fileItem) return;

    if (e.target.closest('.file-actions')) return;

    startX = e.clientX;
    startY = e.clientY;

    contextMenuTimer = setTimeout(() => {
        showContextMenu(e.clientX, e.clientY, fileItem);
    }, 500);
});

fileListContainer.addEventListener('mouseup', e => {
    clearTimeout(contextMenuTimer);
    if (contextMenuVisible) {
        e.preventDefault();
        e.stopPropagation();
    }
}, true);

fileListContainer.addEventListener('mouseleave', e => {
    clearTimeout(contextMenuTimer);
});

fileListContainer.addEventListener('mousemove', e => {
    if (contextMenuTimer) {
        const dist = Math.sqrt(Math.pow(e.clientX - startX, 2) + Math.pow(e.clientY - startY, 2));
        if (dist > 10) {
            clearTimeout(contextMenuTimer);
        }
    }
});

overlay.addEventListener('click', hideContextMenu);

contextMenu.addEventListener('click', e => {
    const action = e.target.dataset.action;
    if (action && currentFileId) {
        switch(action) {
            case 'rename':
                renameFile(currentFileId);
                break;
            case 'delete':
                deleteFile(currentFileId);
                break;
        }
    }
    hideContextMenu();
});

// Touch events
fileListContainer.addEventListener('touchstart', e => {
    const fileItem = e.target.closest('.file-item');
    if (!fileItem) return;
    if (e.target.closest('.file-actions')) return;

    const touch = e.touches[0];
    startX = touch.clientX;
    startY = touch.clientY;

    contextMenuTimer = setTimeout(() => {
        showContextMenu(touch.clientX, touch.clientY, fileItem);
    }, 500);
});

fileListContainer.addEventListener('touchend', e => {
    clearTimeout(contextMenuTimer);
    if (contextMenuVisible) {
        e.preventDefault();
        e.stopPropagation();
    }
}, true);

fileListContainer.addEventListener('touchmove', e => {
    if (contextMenuTimer) {
        const touch = e.touches[0];
        const dist = Math.sqrt(Math.pow(touch.clientX - startX, 2) + Math.pow(touch.clientY - startY, 2));
        if (dist > 15) {
            clearTimeout(contextMenuTimer);
        }
    }
});

// Global listener for drag-and-release on mobile
document.addEventListener('touchend', e => {
    if (!isDragging) return;

    const touch = e.changedTouches[0];
    if (!touch) return;

    const element = document.elementFromPoint(touch.clientX, touch.clientY);
    const menuItem = element?.closest('#file-context-menu .context-menu-item');

    if (menuItem) {
        e.preventDefault(); // Prevent synthesized click
        const action = menuItem.dataset.action;
        if (action && currentFileId) {
            switch(action) {
                case 'rename':
                    renameFile(currentFileId);
                    break;
                case 'delete':
                    deleteFile(currentFileId);
                    break;
            }
        }
    }
    
    hideContextMenu();
}, true); 

fileListContainer.addEventListener('contextmenu', e => {
    e.preventDefault();
});
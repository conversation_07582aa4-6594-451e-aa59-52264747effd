# Math Auto-Complete Examples

## การใช้งาน Math Auto-Complete ในแอป Notes

### เครื่องหมายคณิตศาสตร์ที่รองรับ:

#### 🔢 **เครื่องหมายพื้นฐาน**
- `+` บวก
- `-` ลบ  
- `*` คูณ
- `/` หาร

#### 📱 **เครื่องหมายในโทรศัพท์**
- `×` คูณ (Alt + 0215 หรือ copy-paste)
- `÷` หาร (Alt + 0247 หรือ copy-paste)  
- `−` ลบ (Unicode minus)

### ตัวอย่างการใช้งาน:

#### การคำนวณพื้นฐาน:
```
5 × 3 =          → แสดง ghost: "15"    → กด Tab ได้: "5 × 3 =15"
12 ÷ 4 =         → แสดง ghost: "3"     → กด Tab ได้: "12 ÷ 4 =3"
10 − 3 =         → แสดง ghost: "7"     → กด Tab ได้: "10 − 3 =7"
```

#### การคำนวณซับซ้อน (หลายตัวดำเนินการ):
```
1+2+3+4 =        → แสดง ghost: "10"    → กด Tab ได้: "1+2+3+4=10"
5*2-3+1 =        → แสดง ghost: "8"     → กด Tab ได้: "5*2-3+1=8"
(2+3)*4-1 =      → แสดง ghost: "19"    → กด Tab ได้: "(2+3)*4-1=19"
2^3+5*2 =        → แสดง ghost: "18"    → กด Tab ได้: "2^3+5*2=18"
10÷2+3×4 =       → แสดง ghost: "17"    → กด Tab ได้: "10÷2+3×4=17"
```

#### ฟังก์ชันคณิตศาสตร์ (รองรับสมการซับซ้อนในวงเล็บ):
```
sqrt(25) =       → แสดง ghost: "5"
sqrt(2×8) =      → แสดง ghost: "4"
sqrt(1+2+3+4) =  → แสดง ghost: "3.162"
sin(30) =        → แสดงผลลัพธ์
cos(0) =         → แสดง ghost: "1"
log(10*10) =     → แสดง ghost: "2"
pow(2,1+2) =     → แสดง ghost: "8"
abs(-5+2) =      → แสดง ghost: "3"
```

#### การยกกำลัง:
```
2^3 =            → แสดง ghost: " 8"
5^2 =            → แสดง ghost: " 25"
pow(2,3) =       → แสดง ghost: " 8"
```

### วิธีการใช้งาน:

1. **พิมพ์สมการ** ลงท้ายด้วย `=`
2. **รอ ghost message** ปรากฏ (สีเขียวอ่อน)
3. **เลือกวิธียอมรับ:**
   - **คลิกที่ ghost** เพื่อเติมคำตอบ
   - **กด Tab** เพื่อเติมคำตอบ
   - **กด Enter** เพื่อเติมคำตอบและขึ้นบรรทัดใหม่
4. **กด Esc** เพื่อซ่อน ghost message

### คุณสมบัติพิเศษ:

✅ **การเว้นวรรคอัตโนมัติ**: `= 2` ไม่ใช่ `=2`
✅ **รองรับเครื่องหมายมือถือ**: `×`, `÷`, `−`
✅ **คำนวณซับซ้อน**: วงเล็บ, ลำดับการคำนวณ
✅ **ฟังก์ชันคณิตศาสตร์**: sqrt, sin, cos, log
✅ **ป้องกันข้อผิดพลาด**: ตัวเลขใหญ่เกินไป, สมการผิด
✅ **การเว้นวรรคอัจฉริยะ**: ระบบจัดการช่องว่างอัตโนมัติ
✅ **รองรับ Enter**: กด Enter เพื่อยอมรับและขึ้นบรรทัดใหม่

### เคล็ดลับ:

- ใช้ `×` และ `÷` สำหรับการคูณหารที่ชัดเจน
- ใช้วงเล็บ `()` เพื่อควบคุมลำดับการคำนวณ
- ฟังก์ชันต้องมีวงเล็บ เช่น `sqrt(16)` ไม่ใช่ `sqrt 16`
- Ghost message จะหายไปเมื่อเลื่อนหน้าจอหรือคลิกที่อื่น
- **กด Enter** เพื่อยอมรับคำตอบและเริ่มบรรทัดใหม่ทันที
- **กด Tab** เพื่อยอมรับคำตอบและพิมพ์ต่อในบรรทัดเดิม

### ตัวอย่างผลลัพธ์ที่ได้:

#### **ใช้ Tab (ไม่เว้นวรรค):**
```
พิมพ์: 1+1=
Ghost: 2
กด Tab → ได้: 1+1=2
```

#### **ใช้ Enter (ขึ้นบรรทัดใหม่):**
```
พิมพ์: 5×3=
Ghost: 15
กด Enter → ได้: 5×3=15
                [cursor ที่บรรทัดใหม่]
```

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Complete Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .editor {
            width: 100%;
            height: 300px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            background: #2a2a2a;
            color: white;
            border: 1px solid #444;
            border-radius: 5px;
            resize: vertical;
        }
        
        .instructions {
            margin-bottom: 20px;
            padding: 15px;
            background: #333;
            border-radius: 5px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .instructions ul {
            margin: 10px 0;
        }
        
        .instructions li {
            margin: 5px 0;
        }
        
        .test-examples {
            margin-top: 20px;
            padding: 15px;
            background: #333;
            border-radius: 5px;
        }
        
        .test-examples h3 {
            margin-top: 0;
            color: #2196F3;
        }
        
        .example {
            margin: 10px 0;
            padding: 8px;
            background: #444;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="instructions">
            <h3>🤖 AI Auto-Complete Test</h3>
            <p>This page tests both math and word auto-completion features:</p>
            <ul>
                <li><strong>Math:</strong> Type expressions like "5 + 3 =" or "sqrt(25) ="</li>
                <li><strong>Words:</strong> Type partial words like "hel", "func", "prog"</li>
                <li><strong>Accept:</strong> Press Tab, Enter, or click the ghost suggestion</li>
                <li><strong>Dismiss:</strong> Press Esc or click elsewhere</li>
            </ul>
        </div>
        
        <textarea id="editor" class="editor" placeholder="Try typing:
- Math: 5 + 3 = 
- Math: sqrt(25) =
- Words: hel (for hello)
- Words: func (for function)
- Words: prog (for programming)

Start typing to see auto-complete suggestions!"></textarea>
        
        <div class="test-examples">
            <h3>📝 Test Examples</h3>
            <div class="example">Math: 2 + 2 = (should suggest "4")</div>
            <div class="example">Math: sqrt(16) = (should suggest "4")</div>
            <div class="example">Math: 10 * 5 - 3 = (should suggest "47")</div>
            <div class="example">Word: hel (should suggest "lo" to complete "hello")</div>
            <div class="example">Word: func (should suggest "tion" to complete "function")</div>
            <div class="example">Word: prog (should suggest "ramming" to complete "programming")</div>
        </div>
    </div>

    <script src="./src/ai.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Auto-Complete Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #4CAF50, #2196F3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-section h2 {
            color: #4CAF50;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .editor {
            width: 100%;
            height: 200px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            resize: vertical;
            outline: none;
            transition: border-color 0.3s ease;
        }
        
        .editor:focus {
            border-color: #4CAF50;
        }
        
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .example-card {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .example-card h3 {
            color: #4CAF50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .example-code {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 8px;
            border-radius: 5px;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .feature-list {
            list-style: none;
            margin-top: 15px;
        }
        
        .feature-list li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            color: #FFC107;
            margin-bottom: 15px;
        }
        
        .key-combo {
            background: rgba(0, 0, 0, 0.3);
            padding: 3px 8px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Auto-Complete Demo</h1>
            <p>Intelligent Math & Word Completion for Enhanced Productivity</p>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use</h3>
            <p><strong>Accept Suggestions:</strong> Press <span class="key-combo">Tab</span>, <span class="key-combo">Enter</span>, or click the ghost text</p>
            <p><strong>Dismiss:</strong> Press <span class="key-combo">Esc</span> or click elsewhere</p>
            <p><strong>Math:</strong> Green suggestions for calculations • <strong>Words:</strong> Blue suggestions for text completion</p>
        </div>
        
        <div class="demo-section">
            <h2>🧮 Math Auto-Complete</h2>
            <textarea id="mathEditor" class="editor" placeholder="Try typing math expressions:
• 5 + 3 = 
• sqrt(25) = 
• 10 * 5 - 3 = 
• sin(30) = 
• pow(2,3) = 

Type any math expression ending with '=' to see the result!"></textarea>
            
            <div class="examples">
                <div class="example-card">
                    <h3>Basic Operations</h3>
                    <div class="example-code">5 + 3 = → suggests "8"</div>
                    <div class="example-code">12 ÷ 4 = → suggests "3"</div>
                    <div class="example-code">10 × 5 = → suggests "50"</div>
                </div>
                <div class="example-card">
                    <h3>Functions</h3>
                    <div class="example-code">sqrt(25) = → suggests "5"</div>
                    <div class="example-code">sin(30) = → suggests result</div>
                    <div class="example-code">pow(2,3) = → suggests "8"</div>
                </div>
                <div class="example-card">
                    <h3>Complex Expressions</h3>
                    <div class="example-code">(2+3)*4-1 = → suggests "19"</div>
                    <div class="example-code">2^3+1 = → suggests "9"</div>
                    <div class="example-code">sqrt(2+7) = → suggests "3"</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📝 Word Auto-Complete</h2>
            <textarea id="wordEditor" class="editor" placeholder="Try typing partial words:
• hel (suggests 'hello')
• func (suggests 'function')
• prog (suggests 'programming')
• java (suggests 'javascript')
• comp (suggests 'computer')

Type any word with 3+ characters to see suggestions!"></textarea>
            
            <div class="examples">
                <div class="example-card">
                    <h3>Common Words</h3>
                    <div class="example-code">hel → suggests "lo" (hello)</div>
                    <div class="example-code">wor → suggests "ld" (world)</div>
                    <div class="example-code">exa → suggests "mple" (example)</div>
                </div>
                <div class="example-card">
                    <h3>Programming Terms</h3>
                    <div class="example-code">func → suggests "tion" (function)</div>
                    <div class="example-code">java → suggests "script" (javascript)</div>
                    <div class="example-code">prog → suggests "ramming" (programming)</div>
                </div>
                <div class="example-card">
                    <h3>Context-Aware</h3>
                    <div class="example-code">Learns from your text</div>
                    <div class="example-code">Suggests relevant words</div>
                    <div class="example-code">Smart capitalization</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🚀 Features</h2>
            <ul class="feature-list">
                <li>Real-time math calculation with support for complex expressions</li>
                <li>Intelligent word completion with context awareness</li>
                <li>Mobile-friendly math symbols (×, ÷, −)</li>
                <li>Programming keywords and technical terms</li>
                <li>Smart capitalization preservation</li>
                <li>Visual feedback with color-coded suggestions</li>
                <li>Keyboard shortcuts for quick acceptance</li>
                <li>Extensible dictionary system</li>
            </ul>
        </div>
    </div>

    <script src="./src/ai.js"></script>
</body>
</html>

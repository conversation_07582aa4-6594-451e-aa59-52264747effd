const welcomeText = document.getElementById('welcome-animate');
const continueBtn = document.getElementById('continue-btn');

document.fonts.ready.then(function () {
    const st = SplitText.create("#welcome-animate", { type: "chars", charsClass: "char" });

    st.chars.forEach((char) => {
        gsap.set(char, { attr: { "data-content": char.innerHTML } });
    });

    function runWelcomeText() {
        var tl = gsap.timeline();
        const split = new SplitText("#welcome-animate", { type: "lines" });
        tl.from(split.lines, { opacity: 0, y: "50%", duration: 1.5, ease: "back.out", stagger: 0.2 });

        welcomeText.style.transform = "translate(-50%, -50%) scale(100%)";

        // gentle hover effect
        welcomeText.onpointermove = (e) => {
            st.chars.forEach((char) => {
                const rect = char.getBoundingClientRect();
                const cx = rect.left + rect.width / 2;
                const cy = rect.top + rect.height / 2;
                const dx = e.clientX - cx;
                const dy = e.clientY - cy;
                const dist = Math.sqrt(dx * dx + dy * dy);

                const hoverRadius = window.innerWidth * 0.08; // 8% of viewport width as hover radius
                if (dist < hoverRadius) {
                    const blur_amount = 8 * (1 - (dist / hoverRadius));
                    gsap.to(char, {
                        overwrite: true,
                        duration: 0.5, // Smoother duration for blur
                        y: -7.5,
                        scale: 1.05,
                        filter: "blur(" + blur_amount + "px)", // Progressive blur effect
                        ease: 'power2.out' // Smooth ease
                    });

                    if (!char.scrambleOverlay) {
                        char.scrambleOverlay = document.createElement('span');
                        char.scrambleOverlay.innerHTML = ' ';
                        char.scrambleOverlay.classList.add('scramble-overlay-bg'); // Add the new class
                        char.scrambleOverlay.style.position = 'absolute';
                        char.scrambleOverlay.style.pointerEvents = 'none';
                        char.scrambleOverlay.style.opacity = 0;
                        char.scrambleOverlay.style.transform = 'translate(-50%, -50%) scale(0)';
                        char.scrambleOverlay.style.left = '50%';
                        char.scrambleOverlay.style.top = '50%';
                        char.style.position = 'relative';
                        char.appendChild(char.scrambleOverlay);
                    }
                    if (char.scrambleOverlay && char.scrambleOverlay.parentNode) {
                        gsap.to(char.scrambleOverlay, {
                            overwrite: true,
                            duration: 0.2,
                            opacity: 1,
                            scale: 1,
                            ease: 'power2.out'
                        });
                    }

                } else {
                    gsap.to(char, {
                        overwrite: true,
                        duration: 0.5,
                        y: 0,
                        scale: 1,
                        ease: 'elastic.out(1, 0.3)',
                        filter: "blur(0px)",
                    });
                    // Animate out and remove scramble overlay
                    if (char.scrambleOverlay && char.scrambleOverlay.parentNode) {
                        gsap.to(char.scrambleOverlay, {
                            overwrite: true,
                            duration: 0.3,
                            opacity: 0,
                            scale: 0,
                            onComplete: () => {
                                if (char.scrambleOverlay && char.scrambleOverlay.parentNode) {
                                    char.scrambleOverlay.parentNode.removeChild(char.scrambleOverlay);
                                    char.scrambleOverlay = null;
                                }
                            }
                        });
                    } else {
                        char.scrambleOverlay = null;
                    }
                }
            });
        };
    }

    function renderHelloByLanguage() {
        const taglineElement = document.getElementById('tagline');
        if (taglineElement) {

            taglineSplit = new SplitText(taglineElement, { type: 'words, chars' }),
                taglineChars = taglineSplit.chars;

            var tl = new TimelineMax();

            tl.staggerFrom(taglineChars, .5, { opacity: 0 }, 0.05);

            taglineElement.style.opacity = "1";
        }
    }

    // New color changing logic
    const earthToneColors = [
        "#B5651D", // Warm Brown-Orange
        "#D4A017", // Golden Yellow
        "#8B4513", // Rich Brown
        "#3B7A57", // Deep Olive Green
        "#4682B4", // Steel Blue
        "#A52A2A", // Earthy Red
        "#6A5ACD", // Slate Purple
    ];

    function setRandomTaglineColor() {
        const taglineElement = document.getElementById('tagline');
        if (taglineElement) {
            const randomColor = earthToneColors[Math.floor(Math.random() * earthToneColors.length)];
            gsap.to(taglineElement, { duration: 1, color: randomColor, ease: "power1.inOut" }); // Smooth color transition
        }
    }

    setTimeout(() => {
        runWelcomeText();

        setTimeout(() => {
            welcomeText.style.opacity = "0";
        }, 4800);

        setTimeout(() => {
            // Doing hello by language
            renderHelloByLanguage();
            setInterval(setRandomTaglineColor, 1000);

            continueBtn.style.visibility = "visible";

            setTimeout(() => {
                continueBtn.style.opacity = "1";
            }, 600);

        }, 5800);

    }, 1900);
});
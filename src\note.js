/**
 * Core login for NoteAPP
 */

const stroage_key = 'textEditorData';
const autosave_Interval = 15000;
const anim_duration = 150;
const maxFile_length = 100;

// Pre-compiled regex
const wordRegex = /\s+/;
const htmlEscapeRegex = /[&<>"']/g;

// For XSS prevention
const htmlEscapeMap = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
};

const editor = document.getElementById('editor');
const saveBtn = document.getElementById('saveBtn');
const fileList = document.getElementById('fileList');
const fileListTabs = document.getElementById('fileListTabs');
const fileTitle = document.getElementById('fileTitle');
const wordCount = document.getElementById('wordCount');
const charCount = document.getElementById('charCount');
const fileStatus = document.getElementById('fileStatus');
const fileName = document.getElementById('fileName');

const validateDOMElements = () => {
    const elements = { editor, saveBtn, fileList, fileListTabs, fileTitle, wordCount, charCount, fileStatus, fileName };
    const missing = Object.entries(elements).filter(([name, el]) => !el).map(([name]) => name);

    if (missing.length > 0) {
        throw new Error(`Missing DOM elements: ${missing.join(', ')}`);
    }
};

let appState = {
    files: {
        'welcome': {
            name: 'Welcome',
            content: 'Welcome to NotesApp!\nStart writing your notes here.',
            lastModified: new Date().toISOString()
        }
    },
    currentFile: 'welcome',
    unsavedChanges: false,
    actionsVisibleFileId: null
};

let rafId = null;
let autoSaveInterval = null;

/**
 * Utility Functions
 */
const escapeHtml = (text) => {
    return text.replace(htmlEscapeRegex, (match) => htmlEscapeMap[match]);
};

const validateFileName = (name) => {
    const trimmed = name.trim();
    return trimmed.length > 0 && trimmed.length <= maxFile_length;
};

const generateUniqueId = () => {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const saveAppState = () => {
    try {
        const serialized = JSON.stringify(appState);
        localStorage.setItem(stroage_key, serialized);
        return true;
    } catch (error) {
        console.error('Error saving to localStorage:', error);

        // Handle quota exceeded
        if (error.name === 'QuotaExceededError') {
            alert('Storage quota exceeded. Please delete some files.');
        } else {
            alert('Failed to save data. Please try again.');
        }
        return false;
    }
};

const loadAppState = () => {
    try {
        const saved = localStorage.getItem(stroage_key);
        if (saved) {
            const loadedState = JSON.parse(saved);

            // Validate loaded state structure
            if (loadedState &&
                typeof loadedState.files === 'object' &&
                loadedState.files !== null &&
                typeof loadedState.currentFile === 'string') {
                appState = loadedState;
                return true;
            }
        }
    } catch (error) {
        console.error('Error loading state from localStorage:', error);
        alert('Failed to load saved data. Starting fresh.');
    }
    return false;
};

const updateStatusBar = () => {
    try {
        const content = editor.value;
        const trimmedContent = content.trim();

        const wordCountValue = trimmedContent ? trimmedContent.split(wordRegex).length : 0;
        const charCountValue = content.length;
        const currentFileName = appState.currentFile && appState.files[appState.currentFile]
            ? appState.files[appState.currentFile].name
            : 'Ready';

        wordCount.textContent = `Words: ${wordCountValue}`;
        charCount.textContent = `Characters: ${charCountValue}`;
        fileStatus.textContent = `File: ${escapeHtml(currentFileName)}`;
    } catch (error) {
        console.error('Error updating status bar:', error);
    }
};

const updateSaveButton = () => {
    try {
        if (appState.unsavedChanges) {
            saveBtn.textContent = 'Save *';
            saveBtn.style.background = window.matchMedia('(prefers-color-scheme: light)').matches
                ? 'hsla(0, 55%, 93%, 1.00)'
                : 'hsla(0, 5%, 20%, 1.00)';
            saveBtn.style.background = document.body.classList.contains('light-mode') 
                ? 'hsla(0, 55%, 93%, 1.00)'
                : 'hsla(0, 5%, 20%, 1.00)';
        } else {
            saveBtn.textContent = 'Save';
            saveBtn.style.background = '';
        }
    } catch (error) {
        console.error('Error updating save button:', error);
    }
};

const createFileElement = (fileId, file, isActive, isTab = false) => {
    const fileItem = document.createElement('div');
    fileItem.className = `file-item ${isActive ? 'active' : ''}`;
    fileItem.dataset.fileId = fileId;

    const escapedName = escapeHtml(file.name);

    if (isTab) {
        fileItem.innerHTML = `
            <div class="file-name">${escapedName}</div>
            <button class="close-btn" data-action="delete" data-file-id="${fileId}">
                <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960" width="16px" fill="currentColor">
                    <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                </svg>
            </button>
        `;
    } else {
        fileItem.innerHTML = `
            <div class="file-info sidebarFile-infoContent">
                <div class="file-name">${escapedName}</div>
            </div>
            <div class="file-actions">
                <button class="action-btn" data-action="rename" data-file-id="${fileId}">
                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="currentColor">
                        <path d="M200-200h57l391-391-57-57-391 391v57Zm-80 80v-170l528-527q12-11 26.5-17t30.5-6q16 0 31 6t26 18l55 56q12 11 17.5 26t5.5 30q0 16-5.5 30.5T817-647L290-120H120Zm640-584-56-56 56 56Zm-141 85-28-29 57 57-29-28Z"/>
                    </svg>
                </button>
                <button class="action-btn delete" data-action="delete" data-file-id="${fileId}">
                    <svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 -960 960 960" width="18px" fill="currentColor">
                        <path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z"/>
                    </svg>
                </button>
            </div>
        `;
    }

    return fileItem;
};

const renderFileList = () => {
    try {
        const fragment = document.createDocumentFragment();

        Object.keys(appState.files).forEach(fileId => {
            const file = appState.files[fileId];
            const isActive = fileId === appState.currentFile;
            const fileItem = createFileElement(fileId, file, isActive, false);

            if (fileId === appState.actionsVisibleFileId) {
                fileItem.classList.add('actions-visible');
            }

            fragment.appendChild(fileItem);
        });

        fileList.innerHTML = '';
        fileList.appendChild(fragment);
    } catch (error) {
        console.error('Error rendering file list:', error);
    }
};

const renderFileListTabs = () => {
    try {
        const fragment = document.createDocumentFragment();

        Object.keys(appState.files).forEach(fileId => {
            const file = appState.files[fileId];
            const isActive = fileId === appState.currentFile;
            const fileItem = createFileElement(fileId, file, isActive, true);
            fragment.appendChild(fileItem);
        });

        fileListTabs.innerHTML = '';
        fileListTabs.appendChild(fragment);
    } catch (error) {
        console.error('Error rendering file tabs:', error);
    }
};

const selectFile = (fileId) => {
    try {
        if (!appState.files[fileId]) {
            console.error(`File ${fileId} does not exist`);
            return;
        }

        if (appState.unsavedChanges) {
            const save = confirm('You have unsaved changes. Do you want to save them first?');
            if (save) {
                saveFile();
            }
        }

        appState.currentFile = fileId;
        if (window.innerWidth <= 1100) {
            if (appState.actionsVisibleFileId === fileId) {
                appState.actionsVisibleFileId = null;
            } else {
                appState.actionsVisibleFileId = fileId;
            }
        }
        loadCurrentFile();
        renderFileList();
        renderFileListTabs();
    } catch (error) {
        console.error('Error selecting file:', error);
    }
};

const loadCurrentFile = () => {
    try {
        if (!appState.currentFile) return;

        const file = appState.files[appState.currentFile];
        if (!file) {
            console.error('Current file does not exist');
            return;
        }

        editor.value = file.content;
        fileTitle.textContent = file.name;
        appState.unsavedChanges = false;

        // Batch DOM updates
        requestAnimationFrame(() => {
            updateStatusBar();
            updateSaveButton();
        });
    } catch (error) {
        console.error('Error loading current file:', error);
    }
};

const saveFile = () => {
    try {
        if (!appState.currentFile) return false;

        const fileRef = appState.files[appState.currentFile];
        if (!fileRef) {
            console.error('Attempted to save to an invalid file reference.');
            return false;
        }

        // Immediate UI feedback
        saveBtn.style.transform = 'scale(0.95)';

        const content = editor.value;
        fileRef.content = content;
        fileRef.lastModified = new Date().toISOString();

        appState.unsavedChanges = false;

        // Batch operations
        const saved = saveAppState();
        if (saved) {
            updateSaveButton();
            renderFileList();
        }

        // Reset button animation
        setTimeout(() => {
            saveBtn.style.transform = '';
        }, anim_duration);

        return saved;
    } catch (error) {
        console.error('Error saving file:', error);
        return false;
    }
};

const exportFile = () => {
    try {
        if (!appState.currentFile) return;

        const file = appState.files[appState.currentFile];
        const content = file.content;
        const filename = `${file.name}.txt`;

        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');

        a.href = url;
        a.download = filename;
        a.style.display = 'none';

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error exporting file:', error);
        alert('Failed to export file. Please try again.');
    }
};

const createFile = () => {
    fileName.value = '';
    showModal('createFileModal');
};

const confirmCreateFile = () => {
    try {
        const fileNameValue = fileName.value.trim();

        if (!validateFileName(fileNameValue)) {
            alert(`Enter a valid file name (1-${maxFile_length} characters).`);
            return;
        }

        const existingNames = Object.values(appState.files).map(f => f.name.toLowerCase());
        if (existingNames.includes(fileNameValue.toLowerCase())) {
            alert('A file with this name already exists.');
            return;
        }

        const fileId = generateUniqueId();
        const newFile = {
            name: fileNameValue,
            content: '',
            lastModified: new Date().toISOString()
        };

        appState.files[fileId] = newFile;
        appState.currentFile = fileId;

        loadCurrentFile();
        renderFileList();
        renderFileListTabs();

        if (saveAppState()) {
            closeModal('createFileModal');
        }
    } catch (error) {
        console.error('Error creating file:', error);
        alert('Failed to create file. Please try again.');
    }
};

const deleteFile = (fileId) => {
    try {
        const file = appState.files[fileId];
        if (!file) {
            console.error(`File ${fileId} does not exist`);
            return;
        }

        if (!confirm(`Want to delete ${file.name} file?`)) return;

        delete appState.files[fileId];

        const remainingFiles = Object.keys(appState.files);

        if (remainingFiles.length > 0) {
            if (appState.currentFile === fileId) {
                appState.currentFile = remainingFiles[0];
            }
        } else {
            // Create new welcome file if no files left
            const newFileId = generateUniqueId();
            appState.files[newFileId] = {
                name: 'Welcome',
                content: 'Welcome to NotesApp!\nStart writing your notes here.',
                lastModified: new Date().toISOString()
            };
            appState.currentFile = newFileId;
        }

        // Batch updates
        loadCurrentFile();
        renderFileList();
        renderFileListTabs();
        saveAppState();
    } catch (error) {
        console.error('Error deleting file:', error);
        alert('Failed to delete file. Please try again.');
    }
};

const renameFile = (fileId) => {
    try {
        const file = appState.files[fileId];
        if (!file) {
            console.error(`File ${fileId} does not exist`);
            return;
        }

        const newName = prompt('New file name:', file.name);

        if (newName && validateFileName(newName)) {
            const trimmedName = newName.trim();

            // Check for duplicate names
            const existingNames = Object.entries(appState.files)
                .filter(([id]) => id !== fileId)
                .map(([, f]) => f.name.toLowerCase());

            if (existingNames.includes(trimmedName.toLowerCase())) {
                alert('A file with this name already exists.');
                return;
            }

            file.name = trimmedName;
            file.lastModified = new Date().toISOString();

            if (appState.currentFile === fileId) {
                fileTitle.textContent = trimmedName;
            }

            // Batch updates
            renderFileList();
            renderFileListTabs();
            saveAppState();
        } else if (newName !== null) {
            alert(`Enter a valid file name (1-${maxFile_length} characters).`);
        }
    } catch (error) {
        console.error('Error renaming file:', error);
        alert('Failed to rename file. Please try again.');
    }
};

/**
 * Modal Functions
 */
const showModal = (modalId) => {
    try {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal ${modalId} not found`);
            return;
        }

        modal.removeAttribute('inert');
        modal.classList.add('show');
        modal.setAttribute('aria-hidden', 'false');

        const input = modal.querySelector('input[type="text"]');
        if (input) {
            setTimeout(() => input.focus(), 0);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
    }
};

const closeModal = (modalId) => {
    try {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal ${modalId} not found`);
            return;
        }

        modal.setAttribute('inert', 'true');
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
    } catch (error) {
        console.error('Error closing modal:', error);
    }
};

/**
 * Input handler with RAF throttling
 */
const handleEditorInput = () => {
    appState.unsavedChanges = true;

    if (rafId) {
        cancelAnimationFrame(rafId);
    }

    rafId = requestAnimationFrame(() => {
        updateStatusBar();
        updateSaveButton();
        rafId = null;
    });
};

/**
 * Event Handlers with secure event delegation
 */
const handleFileAction = (e) => {
    const actionBtn = e.target.closest('[data-action]');
    if (!actionBtn) return;

    const action = actionBtn.dataset.action;
    const fileId = actionBtn.dataset.fileId;

    if (!fileId || !appState.files[fileId]) return;

    e.stopPropagation();

    switch (action) {
        case 'delete':
            deleteFile(fileId);
            break;
        case 'rename':
            renameFile(fileId);
            break;
    }
};

const handleFileSelect = (e) => {
    const fileItem = e.target.closest('[data-file-id]');
    if (!fileItem) return;

    const fileId = fileItem.dataset.fileId;
    if (fileId && appState.files[fileId]) {
        selectFile(fileId);
    }
};

/**
 * Initialise Functions
 */
const setupEditor = () => {
    try {
        editor.addEventListener('input', handleEditorInput);

        // Auto-save with cleanup
        autoSaveInterval = setInterval(() => {
            if (appState.unsavedChanges && appState.currentFile) {
                saveFile();
            }
        }, autosave_Interval);

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            const isCtrlCmd = e.ctrlKey || e.metaKey;

            if (isCtrlCmd) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        saveFile();
                        break;
                    case 'b':
                        e.preventDefault();
                        if (typeof toggleSidebar === 'function') {
                            toggleSidebar();
                        }
                        break;
                    case 'n':
                        e.preventDefault();
                        createFile();
                        break;
                }
            }
        });
    } catch (error) {
        console.error('Error setting up editor:', error);
    }
};

const setupEventListeners = () => {
    try {
        fileList.addEventListener('click', handleFileAction);
        fileList.addEventListener('click', handleFileSelect);
        fileListTabs.addEventListener('click', handleFileAction);
        fileListTabs.addEventListener('click', handleFileSelect);

        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
            }
        });

        fileName.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                confirmCreateFile();
            }
        });
    } catch (error) {
        console.error('Error setting up event listeners:', error);
    }
};

/**
 * Utility Functions
 */
const getLocalStorageSize = () => {
    let total = 0;
    try {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const value = localStorage.getItem(key);
            total += (key.length + value.length) * 2;
        }
    } catch (error) {
        console.error('Error calculating localStorage size:', error);
    }
    return total;
};

const formatBytes = (bytes) => {
    if (bytes < 1024) return bytes + " B";
    if (bytes < 1048576) return (bytes / 1024).toFixed(2) + " KB";
    return (bytes / 1048576).toFixed(2) + " MB";
};

const cleanup = () => {
    if (rafId) {
        cancelAnimationFrame(rafId);
        rafId = null;
    }

    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
        autoSaveInterval = null;
    }
};

/**
 * Initialize Application
 */
const initializeApp = () => {
    try {
        validateDOMElements();
        loadAppState();
        renderFileList();
        renderFileListTabs();
        loadCurrentFile();
        setupEditor();
        setupEventListeners();

        console.log('Notes App initialized successfully');
    } catch (error) {
        console.error('Failed to initialize app:', error);
        alert('Failed to initialize the application. Please refresh the page.');
    }
};

window.addEventListener('beforeunload', cleanup);

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

const usedBytes = getLocalStorageSize();
// console.log(`localStorage usage: ${formatBytes(usedBytes)}`);
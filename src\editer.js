// Backspace Behaviours
editor.addEventListener('keydown', (e) => {
    if (e.key === 'Backspace') {
        const { selectionStart, selectionEnd, value } = e.target;

        if (selectionStart === selectionEnd && selectionStart > 0) {
            const lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
            const beforeCursor = value.substring(lineStart, selectionStart);

            // If line only contains spaces and we have 4 spaces before cursor
            if (beforeCursor.match(/^ +$/) && beforeCursor.length >= 4) {
                e.preventDefault();
                const deleteCount = beforeCursor.length % 4 === 0 ? 4 : beforeCursor.length % 4;
                e.target.value = value.slice(0, selectionStart - deleteCount) + value.slice(selectionStart);
                e.target.selectionStart = e.target.selectionEnd = selectionStart - deleteCount;
                return;
            }
        }
    }
});

// Behaviour support
editor.addEventListener('keydown', (e) => {
    if (e.key !== 'Tab') return;

    // Check if AI auto-complete is active - if so, let it handle the Tab
    if (window.mathAutoComplete && window.mathAutoComplete.isActive) {
        return; // Don't prevent default, let AI handle it
    }
    e.preventDefault();

    const { selectionStart, selectionEnd, value } = e.target;

    if (e.shiftKey) {
        // Shift+Tab:
        const lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
        const lineEnd = value.indexOf('\n', selectionEnd);
        const actualLineEnd = lineEnd === -1 ? value.length : lineEnd;

        const selectedText = value.substring(lineStart, actualLineEnd);
        const unindentedText = selectedText.replace(/^    /gm, '');

        e.target.value = value.substring(0, lineStart) + unindentedText + value.substring(actualLineEnd);

        const indentReduction = selectedText.length - unindentedText.length;
        e.target.selectionStart = Math.max(lineStart, selectionStart - Math.min(4, indentReduction));
        e.target.selectionEnd = selectionEnd - indentReduction;
    } else {
        if (selectionStart === selectionEnd) {
            e.target.value = value.slice(0, selectionStart) + '    ' + value.slice(selectionEnd);
            e.target.selectionStart = e.target.selectionEnd = selectionStart + 4;
        } else {
            const lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
            const lineEnd = value.indexOf('\n', selectionEnd);
            const actualLineEnd = lineEnd === -1 ? value.length : lineEnd;

            const selectedText = value.substring(lineStart, actualLineEnd);
            const indentedText = selectedText.replace(/^/gm, '    ');

            e.target.value = value.substring(0, lineStart) + indentedText + value.substring(actualLineEnd);
            e.target.selectionStart = selectionStart + 4;
            e.target.selectionEnd = selectionEnd + (indentedText.length - selectedText.length);
        }
    }
});

editor.addEventListener('keydown', (e) => {
    const pairs = { '(': ')', '[': ']', '{': '}', '"': '"', "'": "'" };
    if (!pairs[e.key]) return;

    const { selectionStart, selectionEnd } = e.target;
    if (selectionStart !== selectionEnd) return;

    setTimeout(() => {
        const pos = e.target.selectionStart;
        e.target.value = e.target.value.slice(0, pos) + pairs[e.key] + e.target.value.slice(pos);
        e.target.selectionStart = e.target.selectionEnd = pos;
    }, 0);
});

editor.focus();

// Set placeholder texts
const placeholderText = `Start writing your notes here...
You can:

    - Create a new project
    - Create a new file
    - Save and open files
    - Export files
    - Manage files and projects

Get started.
`;

editor.placeholder = placeholderText;
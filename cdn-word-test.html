<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN-Based Word Auto-Complete Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #4CAF50, #2196F3, #FF9800);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .loading-status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .loading-status.loading {
            border: 2px solid #FF9800;
            animation: pulse 2s infinite;
        }
        
        .loading-status.loaded {
            border: 2px solid #4CAF50;
        }
        
        .loading-status.error {
            border: 2px solid #f44336;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h3 {
            color: #4CAF50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .editor {
            width: 100%;
            height: 150px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            resize: vertical;
            outline: none;
            transition: border-color 0.3s ease;
            margin-bottom: 15px;
        }
        
        .editor:focus {
            border-color: #4CAF50;
        }
        
        .cdn-sources {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .cdn-source {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
        }
        
        .cdn-source h4 {
            color: #2196F3;
            margin-bottom: 10px;
        }
        
        .cdn-source .url {
            font-family: monospace;
            font-size: 0.9em;
            background: rgba(0, 0, 0, 0.3);
            padding: 5px 8px;
            border-radius: 5px;
            margin: 5px 0;
            word-break: break-all;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            color: #FFC107;
            margin-bottom: 15px;
        }
        
        .key-combo {
            background: rgba(0, 0, 0, 0.3);
            padding: 3px 8px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .console-log {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #4CAF50; }
        .log-warning { color: #FF9800; }
        .log-error { color: #f44336; }
        .log-info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 CDN-Based Word Auto-Complete</h1>
            <p>Loading words dynamically from online sources!</p>
        </div>
        
        <div id="loadingStatus" class="loading-status loading">
            <h3>🔄 Loading Words from CDN...</h3>
            <p>Fetching comprehensive word lists from online sources...</p>
            <div id="loadingProgress">Initializing...</div>
        </div>
        
        <div class="instructions">
            <h3>🎯 How to Test CDN Auto-Complete</h3>
            <p><strong>Wait for loading to complete</strong>, then type partial words (3+ characters) to see suggestions!</p>
            <p><strong>Accept:</strong> Press <span class="key-combo">Tab</span>, <span class="key-combo">Enter</span>, or click the green ghost text</p>
            <p><strong>Sources:</strong> English words from GitHub, Programming terms, Technical vocabulary</p>
        </div>
        
        <div class="test-section">
            <h3>🧪 CDN Word Testing Area</h3>
            <p>Once words are loaded from CDN, you'll have access to 50,000+ words!</p>
            <textarea id="mainEditor" class="editor" placeholder="⏳ Please wait for CDN words to load...

Once loaded, try typing:
• hel → hello (from English dictionary)
• prog → programming (from tech terms)
• java → javascript (from programming terms)
• mach → machine (from AI/ML terms)
• bloc → blockchain (from modern tech)

The system will show you words loaded from online sources!"></textarea>
        </div>
        
        <div class="test-section">
            <h3>🌐 CDN Sources</h3>
            <div class="cdn-sources">
                <div class="cdn-source">
                    <h4>📚 English Dictionary</h4>
                    <p>Comprehensive English word list from GitHub</p>
                    <div class="url">https://raw.githubusercontent.com/dwyl/english-words/master/words_alpha.txt</div>
                    <div class="url">https://cdn.jsdelivr.net/gh/dwyl/english-words@master/words_alpha.txt</div>
                </div>
                
                <div class="cdn-source">
                    <h4>💻 Programming Terms</h4>
                    <p>Languages, frameworks, libraries, and tools</p>
                    <div class="url">Generated from curated programming vocabulary</div>
                </div>
                
                <div class="cdn-source">
                    <h4>🔧 Technical Terms</h4>
                    <p>AI, business, security, and modern tech terms</p>
                    <div class="url">Generated from technical dictionaries</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Loading Statistics</h3>
            <div class="stats">
                <div class="stat-item">
                    <div id="englishWordsCount" class="stat-number">0</div>
                    <div>English Words</div>
                </div>
                <div class="stat-item">
                    <div id="programmingTermsCount" class="stat-number">0</div>
                    <div>Programming Terms</div>
                </div>
                <div class="stat-item">
                    <div id="technicalTermsCount" class="stat-number">0</div>
                    <div>Technical Terms</div>
                </div>
                <div class="stat-item">
                    <div id="totalWordsCount" class="stat-number">0</div>
                    <div>Total Words</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 Console Log</h3>
            <div id="consoleLog" class="console-log">
                <div class="log-entry log-info">🚀 Initializing CDN word auto-complete...</div>
            </div>
        </div>
    </div>

    <script src="./src/ai.js"></script>
    
    <script>
        // Enhanced logging and status tracking
        let originalConsoleLog = console.log;
        let originalConsoleWarn = console.warn;
        let originalConsoleError = console.error;
        
        const consoleLogElement = document.getElementById('consoleLog');
        const loadingStatus = document.getElementById('loadingStatus');
        const loadingProgress = document.getElementById('loadingProgress');
        const mainEditor = document.getElementById('mainEditor');
        
        function addLogEntry(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            consoleLogElement.appendChild(entry);
            consoleLogElement.scrollTop = consoleLogElement.scrollHeight;
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addLogEntry(args.join(' '), 'info');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addLogEntry(args.join(' '), 'warning');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addLogEntry(args.join(' '), 'error');
        };
        
        // Monitor loading progress
        document.addEventListener('DOMContentLoaded', () => {
            if (window.aiAutoComplete) {
                // Monitor the loading process
                const checkLoadingStatus = () => {
                    if (window.aiAutoComplete.isLoadingWords) {
                        loadingProgress.textContent = 'Fetching words from CDN sources...';
                        setTimeout(checkLoadingStatus, 500);
                    } else if (window.aiAutoComplete.cachedWords && window.aiAutoComplete.cachedWords.length > 0) {
                        // Loading complete
                        loadingStatus.className = 'loading-status loaded';
                        loadingStatus.innerHTML = `
                            <h3>✅ CDN Words Loaded Successfully!</h3>
                            <p>Ready for auto-complete with ${window.aiAutoComplete.cachedWords.length} words</p>
                        `;
                        
                        // Update statistics
                        document.getElementById('totalWordsCount').textContent = window.aiAutoComplete.cachedWords.length.toLocaleString();
                        
                        // Enable the editor
                        mainEditor.placeholder = `🎉 CDN words loaded! Try typing:
• hel → hello
• prog → programming  
• java → javascript
• mach → machine
• bloc → blockchain

Start typing any word with 3+ characters!`;
                        mainEditor.disabled = false;
                        
                        addLogEntry(`🎉 CDN loading complete! ${window.aiAutoComplete.cachedWords.length} words available`, 'success');
                    } else {
                        // Loading failed
                        loadingStatus.className = 'loading-status error';
                        loadingStatus.innerHTML = `
                            <h3>❌ CDN Loading Failed</h3>
                            <p>Using fallback word list. Check console for details.</p>
                        `;
                        addLogEntry('❌ CDN loading failed, using fallback words', 'error');
                    }
                };
                
                setTimeout(checkLoadingStatus, 1000);
            } else {
                addLogEntry('❌ AI Auto-Complete not found', 'error');
            }
        });
    </script>
</body>
</html>
